/*
 * 🔧 أنماط مكون StickyContainer المحسّن
 * يحل مشكلة عدم عمل خاصية sticky مع مكتبة react-custom-scrollbars
 * مع تحسينات للسلاسة والاستجابة الفورية
 */

/* تحسين مظهر المكون مع انتقالات أكثر سلاسة */
.sticky-content {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 50;
}

/* تحسين الأداء مع دعم أفضل للحركة */
.sticky-content {
  will-change: transform, position, top, width;
  backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* حالة الوضع الثابت */
.sticky-content.is-sticky {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .sticky-content.is-sticky {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

/* تخصيص شريط التمرير */
.sticky-content::-webkit-scrollbar {
  width: 4px;
}

.sticky-content::-webkit-scrollbar-track {
  background: transparent;
}

.sticky-content::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

.dark .sticky-content::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}