/*
 * 🔧 أنماط مكون StickyContainer البسيط والفعال
 * حل جذري باستخدام CSS sticky الأصلي
 */

/* تطبيق sticky بشكل صحيح */
.sticky-content {
  position: -webkit-sticky;
  position: sticky;
  z-index: 5;
  transition: box-shadow 0.2s ease;
}

/* تحسين الأداء */
.sticky-content {
  will-change: auto;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* تأثير الظل عند التثبيت */
.sticky-content:not(:first-child) {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .sticky-content:not(:first-child) {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* تخصيص شريط التمرير */
.sticky-content::-webkit-scrollbar {
  width: 4px;
}

.sticky-content::-webkit-scrollbar-track {
  background: transparent;
}

.sticky-content::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

.dark .sticky-content::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}