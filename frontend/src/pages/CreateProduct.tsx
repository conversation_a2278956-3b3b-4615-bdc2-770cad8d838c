import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  FiArrowLeft,
  FiSave,
  FiPackage,
  FiDollarSign,
  FiImage,
  FiSettings,
  FiChevronDown,
  FiChevronUp,
  FiCode,
  FiX
} from 'react-icons/fi';
import { FaArrowLeft, FaBarcode } from 'react-icons/fa';
import useProductStore from '../stores/productStore';
import StickyContainer from '../components/StickyContainer';

// Import unified components
import { SelectInput } from '../components/inputs';
import BarcodeInput from '../components/inputs/BarcodeInput';

// Import form sections
import BasicInfoSection from '../components/product/ProductForm/BasicInfoSection';
import PricingInventorySection from '../components/product/ProductForm/PricingInventorySection';
import ImageSection from '../components/product/ProductForm/ImageSection';
import AdditionalFieldsSection from '../components/product/ProductForm/AdditionalFieldsSection';

const CreateProduct: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const stickyBarRef = useRef<HTMLDivElement>(null);

  const {
    createProductAdvanced,
    updateProductAdvanced,
    loading
  } = useProductStore();

  // Form state
  const [formData, setFormData] = useState<any>({
    name: '',
    description: '',
    sku: '',
    item_barcode: '',
    price: '',
    cost_price: '',
    quantity: '',
    min_quantity: '',
    warehouse_id: '',
    category_id: '',
    subcategory_id: '',
    brand_id: '',
    unit_id: '',
    is_active: true,
    product_type: 'single',
    tax_type_id: null,
    tax_rate_id: null,
    discount_type: 'percentage',
    discount_value: '',
    barcode_symbology: 'CODE128',
    slug: '',
    warranty_type: '',
    manufacturer: '',
    manufactured_date: '',
    expiry_date: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showBarcodeSettings, setShowBarcodeSettings] = useState(true);

  // Section states for collapsible sections
  const [openSections, setOpenSections] = useState({
    productInfo: true,
    pricing: true,
    images: false,
    customFields: false
  });

  // Load product data if editing
  useEffect(() => {
    if (id && id !== 'new') {
      loadProduct(parseInt(id));
    }
  }, [id]);

  // TODO: Implement loadProduct when getProductById is available in store
  const loadProduct = async (productId: number) => {
    console.log('loadProduct needs implementation for ID:', productId);
  };

  // Generate SKU
  const generateSKU = () => {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `SKU${timestamp}${random}`;
  };

  // Generate Barcode
  const generateBarcode = () => {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${timestamp}${random}`;
  };

  // Toggle section
  const toggleSection = (section: keyof typeof openSections) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };



  const updateFormData = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = 'اسم المنتج مطلوب';
    }

    if (!formData.price || parseFloat(formData.price) <= 0) {
      newErrors.price = 'السعر مطلوب ويجب أن يكون أكبر من صفر';
    }

    if (!formData.cost_price || parseFloat(formData.cost_price) <= 0) {
      newErrors.cost_price = 'سعر التكلفة مطلوب ويجب أن يكون أكبر من صفر';
    }

    // ملاحظة: الكمية والمستودع يتم إدارتهما في نظام المستودعات المنفصل
    // هنا نركز على البيانات الأساسية للمنتج فقط

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const productData = {
        ...formData,
        price: parseFloat(formData.price),
        cost_price: parseFloat(formData.cost_price),
        quantity: parseInt(formData.quantity),
        min_quantity: parseInt(formData.min_quantity || '0'),
        discount_value: parseFloat(formData.discount_value || '0')
      };

      if (id && id !== 'new') {
        await updateProductAdvanced(parseInt(id), productData);
      } else {
        await createProductAdvanced(productData);
      }
      
      navigate('/products');
    } catch (error) {
      console.error('خطأ في حفظ المنتج:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/products');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 space-y-6">
      {/* Header - Following the exact pattern from Sales page */}
      <div className="mb-6">
        <div className="relative rounded-xl bg-gradient-to-l from-primary-50/40 via-primary-50/20 to-white dark:from-primary-900/20 dark:via-primary-900/10 dark:to-gray-800 border border-gray-200 dark:border-gray-700" style={{ boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)' }}>
          {/* خط نقش علوي للتأثير المميز */}
          <div className="absolute -top-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>

          {/* خط نقش سفلي للتأثير المميز */}
          <div className="absolute -bottom-0.5 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-primary-300/30 to-transparent dark:from-transparent dark:via-primary-600/20 dark:to-transparent"></div>
          <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center p-4 sm:p-6 gap-4">
            <div className="flex items-center min-w-0 flex-1">
              <button
                onClick={handleCancel}
                className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 ease-in-out shadow-lg hover:shadow-xl flex-shrink-0 border-2 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"
                title="العودة للرئيسية"
              >
                <FaArrowLeft className="text-sm" />
              </button>
              <div className="mr-3 sm:mr-4 min-w-0 flex-1">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
                  <FiPackage className="ml-2 sm:ml-3 text-primary-600 dark:text-primary-400 flex-shrink-0" />
                  <span className="truncate">{id && id !== 'new' ? 'تعديل المنتج' : 'إنشاء منتج جديد'}</span>
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1 hidden sm:block">
                  {id && id !== 'new' ? 'تعديل بيانات المنتج الأساسية' : 'إضافة منتج جديد (البيانات الأساسية - المخزون منفصل)'}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3 sm:gap-4 flex-wrap lg:flex-nowrap">
              <button
                onClick={handleCancel}
                className="bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium min-w-[140px] focus:outline-none focus:ring-4 focus:ring-gray-500/20 shadow-sm hover:shadow-md"
              >
                إلغاء
              </button>
              <button
                onClick={handleSubmit}
                disabled={isSubmitting || loading}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl transition-all duration-200 ease-in-out border-2 border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-4 focus:ring-primary-500/20 shadow-lg hover:shadow-xl min-w-[140px] gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiSave className="w-4 h-4" />
                {isSubmitting ? 'جاري الحفظ...' : (id && id !== 'new' ? 'حفظ التغييرات' : 'حفظ المنتج')}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Main Content - Two Column Layout */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Main Content Column */}
        <div className="flex-1 space-y-6 order-2 lg:order-1">
          {/* Product Information Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <button
              onClick={() => toggleSection('productInfo')}
              className="w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                  <FiPackage className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  معلومات المنتج الأساسية
                </h3>
              </div>
              {openSections.productInfo ? (
                <FiChevronUp className="w-5 h-5 text-gray-500" />
              ) : (
                <FiChevronDown className="w-5 h-5 text-gray-500" />
              )}
            </button>

            {openSections.productInfo && (
              <div className="px-6 pb-6 border-t border-gray-100 dark:border-gray-700">
                <div className="pt-6">
                  <BasicInfoSection
                    formData={formData}
                    updateFormData={updateFormData}
                    errors={errors}
                    generateSKU={generateSKU}
                    generateBarcode={generateBarcode}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Pricing Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <button
              onClick={() => toggleSection('pricing')}
              className="w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                  <FiDollarSign className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  التسعير والضرائب
                </h3>
              </div>
              {openSections.pricing ? (
                <FiChevronUp className="w-5 h-5 text-gray-500" />
              ) : (
                <FiChevronDown className="w-5 h-5 text-gray-500" />
              )}
            </button>
            {openSections.pricing && (
              <div className="px-6 pb-6 border-t border-gray-100 dark:border-gray-700">
                <div className="pt-6">
                  <PricingInventorySection
                    formData={formData}
                    updateFormData={updateFormData}
                    errors={errors}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Images Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <button
              onClick={() => toggleSection('images')}
              className="w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                  <FiImage className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  الصور
                </h3>
              </div>
              {openSections.images ? (
                <FiChevronUp className="w-5 h-5 text-gray-500" />
              ) : (
                <FiChevronDown className="w-5 h-5 text-gray-500" />
              )}
            </button>
            {openSections.images && (
              <div className="px-6 pb-6 border-t border-gray-100 dark:border-gray-700">
                <div className="pt-6">
                  <ImageSection
                    formData={formData}
                    updateFormData={updateFormData}
                    errors={errors}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Custom Fields Section */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
            <button
              onClick={() => toggleSection('customFields')}
              className="w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                  <FiSettings className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  الحقول المخصصة
                </h3>
              </div>
              {openSections.customFields ? (
                <FiChevronUp className="w-5 h-5 text-gray-500" />
              ) : (
                <FiChevronDown className="w-5 h-5 text-gray-500" />
              )}
            </button>
            {openSections.customFields && (
              <div className="px-6 pb-6 border-t border-gray-100 dark:border-gray-700">
                <div className="pt-6">
                  <AdditionalFieldsSection
                    formData={formData}
                    updateFormData={updateFormData}
                    errors={errors}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Sidebar Column - Using Enhanced StickyContainer */}
        <div className="lg:w-80 order-1 lg:order-2">
          <StickyContainer
            topOffset={100}
            className="w-full"
            stickyClass="w-full"
            smoothTransition={true}
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 ease-in-out hover:shadow-xl">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-l from-primary-50/40 via-primary-50/20 to-white dark:from-primary-900/20 dark:via-primary-900/10 dark:to-gray-800">
                <h3 className="text-md font-semibold text-gray-900 dark:text-white flex items-center">
                  <FiSettings className="ml-2 text-primary-600 dark:text-primary-400" />
                  أدوات المنتج
                </h3>
              </div>
              
              <div className="p-4 space-y-4">
                {/* Save and Cancel Buttons */}
                <div className="space-y-2">
                  <button
                    onClick={handleSubmit}
                    disabled={isSubmitting || loading}
                    className="w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-all duration-200 ease-in-out border border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-2 focus:ring-primary-500/20 shadow-md hover:shadow-lg gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FiSave className="w-4 h-4" />
                    {isSubmitting ? 'جاري الحفظ...' : 'حفظ المنتج'}
                  </button>
                  
                  <button
                    onClick={handleCancel}
                    className="w-full bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg transition-all duration-200 ease-in-out border border-gray-300 dark:border-gray-600 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-2 focus:ring-gray-500/20 shadow-sm hover:shadow-md"
                  >
                    <FiX className="w-4 h-4 ml-1" />
                    إلغاء
                  </button>
                </div>
                
                {/* Barcode Settings Section */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                  <button
                    onClick={() => setShowBarcodeSettings(!showBarcodeSettings)}
                    className="w-full px-4 py-3 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-7 h-7 bg-primary-100 dark:bg-primary-900/30 rounded-lg flex items-center justify-center">
                        <FiCode className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                      </div>
                      <h3 className="text-md font-medium text-gray-900 dark:text-white">
                        إعدادات الباركود
                      </h3>
                    </div>
                    {showBarcodeSettings ? (
                      <FiChevronUp className="w-5 h-5 text-gray-500" />
                    ) : (
                      <FiChevronDown className="w-5 h-5 text-gray-500" />
                    )}
                  </button>
                  
                  {showBarcodeSettings && (
                    <div className="px-4 pb-4 pt-2 border-t border-gray-100 dark:border-gray-700 space-y-3">
                      {/* استخدام مكون SelectInput من المشروع */}
                      <div className="mb-4">
                        <SelectInput
                          label="نوع الباركود"
                          name="barcode_symbology"
                          value={formData.barcode_symbology}
                          onChange={(value) => updateFormData('barcode_symbology', value)}
                          options={[
                            { value: 'CODE128', label: 'Code 128' },
                            { value: 'UPC_A', label: 'UPC-A' },
                            { value: 'EAN_13', label: 'EAN-13' },
                            { value: 'EAN_8', label: 'EAN-8' },
                            { value: 'CODE39', label: 'Code 39' },
                            { value: 'CODE93', label: 'Code 93' },
                            { value: 'CODABAR', label: 'Codabar' }
                          ]}
                          icon={<FiCode className="w-4 h-4" />}
                          portal={true} // استخدام خاصية portal لضمان ظهور القائمة خارج حدود القسم
                        />
                      </div>
                      
                      {/* مكون إدخال الباركود مع زر التوليد */}
                      <div className="mb-4">
                        {/* العنوان مع زر التوليد */}
                        <div className="flex items-center justify-between mb-2">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            باركود العنصر
                          </label>
                          <button
                            type="button"
                            onClick={() => {
                              try {
                                const newBarcode = generateBarcode();
                                updateFormData('item_barcode', newBarcode);
                              } catch (error) {
                                console.error('خطأ في توليد الباركود:', error);
                              }
                            }}
                            className="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-200 cursor-pointer flex items-center gap-1"
                          >
                            <FaBarcode className="w-3 h-3" />
                            توليد
                          </button>
                        </div>

                        <BarcodeInput
                          name="item_barcode"
                          value={formData.item_barcode}
                          onChange={(value) => updateFormData('item_barcode', value)}
                          placeholder="123456789012"
                          error={errors.item_barcode}
                          showGenerateButton={false}
                        />
                      </div>
                      
                      {/* مكون إدخال SKU مع زر التوليد */}
                      <div className="mb-4">
                        {/* العنوان مع زر التوليد */}
                        <div className="flex items-center justify-between mb-2">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            الباركود الداخلي (SKU)
                          </label>
                          <button
                            type="button"
                            onClick={() => {
                              try {
                                const newSKU = generateSKU();
                                updateFormData('sku', newSKU);
                              } catch (error) {
                                console.error('خطأ في توليد SKU:', error);
                              }
                            }}
                            className="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-200 cursor-pointer flex items-center gap-1"
                          >
                            <FaBarcode className="w-3 h-3" />
                            توليد
                          </button>
                        </div>

                        <BarcodeInput
                          name="sku"
                          value={formData.sku}
                          onChange={(value) => updateFormData('sku', value)}
                          placeholder="SKU123456"
                          error={errors.sku}
                          showGenerateButton={false}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </StickyContainer>
        </div>
      </div>
    </div>
  );
};

export default CreateProduct;
