import React, { useRef, useEffect, useState } from 'react';

interface StickyContainerProps {
  children: React.ReactNode;
  className?: string;
  topOffset?: number;
  stickyClass?: string;
  smoothTransition?: boolean;
}

/**
 * مكون StickyContainer محسّن
 * يوفر حاوية تدعم خاصية sticky بشكل متوافق مع react-custom-scrollbars
 * مع تحسينات للسلاسة والاستجابة الفورية
 */
const StickyContainer: React.FC<StickyContainerProps> = ({
  children,
  className = '',
  topOffset = 24,
  stickyClass = 'sticky-content',
  smoothTransition = true
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const stickyRef = useRef<HTMLDivElement>(null);
  const [isSticky, setIsSticky] = useState(false);

  useEffect(() => {
    const container = containerRef.current;
    const stickyElement = stickyRef.current;

    if (!container || !stickyElement) return;

    // تحديد ما إذا كان المتصفح يدعم IntersectionObserver
    if ('IntersectionObserver' in window) {
      // إنشاء مراقب للتقاطع مع إعدادات محسّنة للاستجابة الفورية
      const observer = new IntersectionObserver(
        () => {
          // تحسين الاستجابة: التحقق من موضع العنصر بدقة أكبر
          const containerRect = container.getBoundingClientRect();
          const shouldBeSticky = containerRect.top <= topOffset;

          if (shouldBeSticky !== isSticky) {
            setIsSticky(shouldBeSticky);

            // استخدام requestAnimationFrame لتحسين الأداء والسلاسة
            requestAnimationFrame(() => {
              if (shouldBeSticky) {
                const containerRect = container.getBoundingClientRect();
                stickyElement.style.position = 'fixed';
                stickyElement.style.top = `${topOffset}px`;
                stickyElement.style.left = `${containerRect.left}px`;
                stickyElement.style.width = `${container.offsetWidth}px`;
                stickyElement.style.zIndex = '20';
                stickyElement.classList.add('is-sticky');
                if (smoothTransition) {
                  stickyElement.style.transition = 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                }
              } else {
                stickyElement.style.position = 'static';
                stickyElement.style.top = 'auto';
                stickyElement.style.left = 'auto';
                stickyElement.style.width = '100%';
                stickyElement.style.zIndex = 'auto';
                stickyElement.classList.remove('is-sticky');
                if (smoothTransition) {
                  stickyElement.style.transition = 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                }
              }
            });
          }
        },
        {
          // إعدادات محسّنة للاستجابة الفورية
          threshold: [0, 0.1, 1],
          rootMargin: `0px 0px 0px 0px`
        }
      );

      // إضافة مراقب التمرير للاستجابة الفورية
      const findScrollableParent = (element: HTMLElement | null): EventTarget & { addEventListener: Function; removeEventListener: Function } => {
        if (!element) {
          return window as EventTarget & { addEventListener: Function; removeEventListener: Function };
        }
        let parent = element.parentElement;
        while (parent) {
          const style = window.getComputedStyle(parent);
          if (style.overflow === 'auto' || style.overflow === 'scroll' || style.overflowY === 'auto' || style.overflowY === 'scroll') {
            return parent as EventTarget & { addEventListener: Function; removeEventListener: Function };
          }
          parent = parent.parentElement;
        }
        return window as EventTarget & { addEventListener: Function; removeEventListener: Function };
      };

      const scrollableParent = findScrollableParent(container);

      let ticking = false;
      const handleScroll = () => {
        if (!container || !stickyElement) return;

        if (!ticking) {
          requestAnimationFrame(() => {
            const containerRect = container.getBoundingClientRect();
            const shouldBeSticky = containerRect.top <= topOffset;

            if (shouldBeSticky !== isSticky) {
              setIsSticky(shouldBeSticky);

              if (shouldBeSticky) {
                const containerRect = container.getBoundingClientRect();
                stickyElement.style.position = 'fixed';
                stickyElement.style.top = `${topOffset}px`;
                stickyElement.style.left = `${containerRect.left}px`;
                stickyElement.style.width = `${container.offsetWidth}px`;
                stickyElement.style.zIndex = '20';
                stickyElement.classList.add('is-sticky');
                if (smoothTransition) {
                  stickyElement.style.transition = 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                }
              } else {
                stickyElement.style.position = 'static';
                stickyElement.style.top = 'auto';
                stickyElement.style.left = 'auto';
                stickyElement.style.width = '100%';
                stickyElement.style.zIndex = 'auto';
                stickyElement.classList.remove('is-sticky');
                if (smoothTransition) {
                  stickyElement.style.transition = 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                }
              }
            }

            ticking = false;
          });

          ticking = true;
        }
      };

      const handleResize = () => {
        if (!container || !stickyElement) return;

        if (stickyElement.style.position === 'fixed') {
          const containerRect = container.getBoundingClientRect();
          stickyElement.style.width = `${container.offsetWidth}px`;
          stickyElement.style.left = `${containerRect.left}px`;
        }
      };

      // بدء مراقبة الحاوية والتمرير
      observer.observe(container);
      scrollableParent.addEventListener('scroll', handleScroll, { passive: true });
      window.addEventListener('resize', handleResize, { passive: true });

      // استدعاء أولي للتحقق من الحالة
      handleScroll();

      // تنظيف المراقبين عند إلغاء تحميل المكون
      return () => {
        observer.disconnect();
        scrollableParent.removeEventListener('scroll', handleScroll);
        window.removeEventListener('resize', handleResize);
      };
    } else {
      // Fallback for browsers that don't support IntersectionObserver
      const findScrollableParent = (element: HTMLElement | null): EventTarget & { addEventListener: Function; removeEventListener: Function } => {
        if (!element) {
          return window as EventTarget & { addEventListener: Function; removeEventListener: Function };
        }
        let parent = element.parentElement;
        while (parent) {
          const style = window.getComputedStyle(parent);
          if (style.overflow === 'auto' || style.overflow === 'scroll' || style.overflowY === 'auto' || style.overflowY === 'scroll') {
            return parent as EventTarget & { addEventListener: Function; removeEventListener: Function };
          }
          parent = parent.parentElement;
        }
        return window as EventTarget & { addEventListener: Function; removeEventListener: Function };
      };

      const scrollableParent = findScrollableParent(container);
      
      let ticking = false;
      const handleScroll = () => {
        if (!container || !stickyElement) return;

        if (!ticking) {
          requestAnimationFrame(() => {
            const containerRect = container.getBoundingClientRect();
            const shouldBeSticky = containerRect.top <= topOffset;

            if (shouldBeSticky !== isSticky) {
              setIsSticky(shouldBeSticky);

              if (shouldBeSticky) {
                stickyElement.style.position = 'fixed';
                stickyElement.style.top = `${topOffset}px`;
                stickyElement.style.left = `${containerRect.left}px`;
                stickyElement.style.width = `${container.offsetWidth}px`;
                stickyElement.style.zIndex = '20';
                stickyElement.classList.add('is-sticky');
                if (smoothTransition) {
                  stickyElement.style.transition = 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                }
              } else {
                stickyElement.style.position = 'static';
                stickyElement.style.top = 'auto';
                stickyElement.style.left = 'auto';
                stickyElement.style.width = '100%';
                stickyElement.style.zIndex = 'auto';
                stickyElement.classList.remove('is-sticky');
                if (smoothTransition) {
                  stickyElement.style.transition = 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                }
              }
            }

            ticking = false;
          });

          ticking = true;
        }
      };
      
      const handleResize = () => {
        if (!container || !stickyElement) return;

        if (stickyElement.style.position === 'fixed') {
          const containerRect = container.getBoundingClientRect();
          stickyElement.style.width = `${container.offsetWidth}px`;
          stickyElement.style.left = `${containerRect.left}px`;
        }
      };

      scrollableParent.addEventListener('scroll', handleScroll, { passive: true });
      (window as Window & typeof globalThis).addEventListener('resize', handleResize, { passive: true });
      
      handleScroll();
      
      return () => {
        scrollableParent.removeEventListener('scroll', handleScroll);
        (window as Window & typeof globalThis).removeEventListener('resize', handleResize);
      };
    }
  }, [topOffset, isSticky, smoothTransition]);
  
  return (
    <div ref={containerRef} className={className}>
      <div ref={stickyRef} className={stickyClass}>
        {children}
      </div>
    </div>
  );
};

export default StickyContainer;
