import React, { useRef, useEffect } from 'react';

interface StickyContainerProps {
  children: React.ReactNode;
  className?: string;
  topOffset?: number;
  stickyClass?: string;
}

/**
 * مكون StickyContainer بسيط وفعال
 * حل جذري للمشكلة باستخدام CSS sticky مع JavaScript fallback
 */
const StickyContainer: React.FC<StickyContainerProps> = ({
  children,
  className = '',
  topOffset = 24,
  stickyClass = 'sticky-content'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // تطبيق sticky مباشرة على العنصر مع التأكد من الحاوي الأب
    container.style.position = 'sticky';
    container.style.top = `${topOffset}px`;
    container.style.zIndex = '5';

    // التأكد من أن الحاوي الأب يدعم sticky
    let parent = container.parentElement;
    while (parent && parent !== document.body) {
      const computedStyle = window.getComputedStyle(parent);
      if (computedStyle.overflow === 'hidden' || computedStyle.overflowY === 'hidden') {
        parent.style.overflow = 'visible';
        console.log('تم تغيير overflow للحاوي الأب إلى visible');
      }
      parent = parent.parentElement;
    }

    console.log('تم تطبيق sticky على العنصر مع topOffset:', topOffset);

    // اختبار sticky بعد تحميل الصفحة وإضافة fallback إذا لم يعمل
    setTimeout(() => {
      const rect = container.getBoundingClientRect();
      const computedStyle = window.getComputedStyle(container);
      console.log('موضع العنصر:', {
        top: rect.top,
        topOffset,
        position: container.style.position,
        computedPosition: computedStyle.position
      });

      // إذا لم يعمل sticky، استخدم JavaScript fallback
      if (computedStyle.position !== 'sticky' && computedStyle.position !== '-webkit-sticky') {
        console.log('CSS sticky لا يعمل، استخدام JavaScript fallback');

        const handleScroll = () => {
          const rect = container.getBoundingClientRect();
          const shouldBeFixed = rect.top <= topOffset;

          if (shouldBeFixed && container.style.position !== 'fixed') {
            container.style.position = 'fixed';
            container.style.top = `${topOffset}px`;
            container.style.left = `${rect.left}px`;
            container.style.width = `${container.offsetWidth}px`;
            console.log('تم تطبيق fixed position');
          } else if (!shouldBeFixed && container.style.position === 'fixed') {
            container.style.position = 'sticky';
            container.style.top = `${topOffset}px`;
            container.style.left = 'auto';
            container.style.width = 'auto';
            console.log('تم إرجاع sticky position');
          }
        };

        window.addEventListener('scroll', handleScroll, { passive: true });

        // تنظيف event listener
        return () => {
          window.removeEventListener('scroll', handleScroll);
        };
      }
    }, 1000);
  }, [topOffset]);

  return (
    <div
      ref={containerRef}
      className={`${className} ${stickyClass}`}
      style={{
        position: 'sticky',
        top: `${topOffset}px`,
        zIndex: 5
      }}
    >
      {children}
    </div>
  );
};

export default StickyContainer;
