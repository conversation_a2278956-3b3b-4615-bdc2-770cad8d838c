import React, { useRef, useEffect, useState } from 'react';

interface StickyContainerProps {
  children: React.ReactNode;
  className?: string;
  topOffset?: number;
  stickyClass?: string;
  smoothTransition?: boolean;
}

/**
 * مكون StickyContainer محسّن
 * يوفر حاوية تدعم خاصية sticky بشكل متوافق مع react-custom-scrollbars
 * مع تحسينات للسلاسة والاستجابة الفورية
 */
const StickyContainer: React.FC<StickyContainerProps> = ({
  children,
  className = '',
  topOffset = 24,
  stickyClass = 'sticky-content',
  smoothTransition = true
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const stickyRef = useRef<HTMLDivElement>(null);
  const [isSticky, setIsSticky] = useState(false);

  useEffect(() => {
    const container = containerRef.current;
    const stickyElement = stickyRef.current;

    if (!container || !stickyElement) return;

    // استخدام window مباشرة لضمان عمل التمرير
    const scrollableParent = window;

    let ticking = false;
    let lastStickyState = isSticky;

    const handleScroll = () => {
      if (!container || !stickyElement) return;

      if (!ticking) {
        requestAnimationFrame(() => {
          const containerRect = container.getBoundingClientRect();

          // منطق بسيط وواضح: يصبح ثابتاً عندما يصل الجزء العلوي من الحاوية إلى topOffset
          const shouldBeSticky = containerRect.top <= topOffset;

          // إضافة تشخيص مؤقت
          console.log('StickyContainer Debug:', {
            containerTop: containerRect.top,
            topOffset,
            shouldBeSticky,
            lastStickyState,
            isSticky
          });

          // تجنب التحديث المتكرر إذا لم تتغير الحالة
          if (shouldBeSticky !== lastStickyState) {
            lastStickyState = shouldBeSticky;
            setIsSticky(shouldBeSticky);

            console.log('تغيير الحالة إلى:', shouldBeSticky ? 'ثابت' : 'عادي');

            if (shouldBeSticky) {
              stickyElement.style.position = 'fixed';
              stickyElement.style.top = `${topOffset}px`;
              stickyElement.style.left = `${containerRect.left}px`;
              stickyElement.style.width = `${container.offsetWidth}px`;
              stickyElement.style.zIndex = '5';
              stickyElement.classList.add('is-sticky');
              if (smoothTransition) {
                stickyElement.style.transition = 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
              }
            } else {
              stickyElement.style.position = 'static';
              stickyElement.style.top = 'auto';
              stickyElement.style.left = 'auto';
              stickyElement.style.width = '100%';
              stickyElement.style.zIndex = 'auto';
              stickyElement.classList.remove('is-sticky');
              if (smoothTransition) {
                stickyElement.style.transition = 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
              }
            }
          }

          ticking = false;
        });

        ticking = true;
      }
    };

    const handleResize = () => {
      if (!container || !stickyElement) return;

      if (stickyElement.style.position === 'fixed') {
        const containerRect = container.getBoundingClientRect();
        stickyElement.style.width = `${container.offsetWidth}px`;
        stickyElement.style.left = `${containerRect.left}px`;
      }
    };

    // بدء مراقبة التمرير وتغيير الحجم
    console.log('إضافة مراقب التمرير إلى window');
    scrollableParent.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleResize, { passive: true });

    // استدعاء أولي للتحقق من الحالة
    console.log('استدعاء أولي لـ handleScroll');
    handleScroll();

    // تنظيف المراقبين عند إلغاء تحميل المكون
    return () => {
      scrollableParent.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, [topOffset, isSticky, smoothTransition]);
  
  return (
    <div ref={containerRef} className={className}>
      <div ref={stickyRef} className={stickyClass}>
        {children}
      </div>
    </div>
  );
};

export default StickyContainer;
