import React from 'react';
import { FiSettings, FiSave, FiX } from 'react-icons/fi';
import StickyContainer from './StickyContainer';

/**
 * مكون اختبار لـ StickyContainer المحسّن
 * يمكن استخدامه لاختبار التحسينات الجديدة
 */
const StickyContainerTest: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* المحتوى الرئيسي */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                محتوى طويل لاختبار التمرير
              </h2>
              
              {/* محتوى طويل لإجبار التمرير */}
              {Array.from({ length: 20 }, (_, i) => (
                <div key={i} className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                    قسم رقم {i + 1}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    هذا نص تجريبي لاختبار التمرير والتأكد من أن مكون StickyContainer يعمل بشكل صحيح.
                    عندما تقوم بالتمرير لأسفل، يجب أن يصبح الشريط الجانبي ثابتاً في الأعلى بمجرد لمس الحافة العلوية.
                    التحسينات الجديدة تضمن استجابة فورية وحركة سلسة.
                  </p>
                  <div className="mt-3 flex flex-wrap gap-2">
                    <span className="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 rounded-full text-sm">
                      علامة {i + 1}
                    </span>
                    <span className="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm">
                      اختبار
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* الشريط الجانبي مع StickyContainer */}
          <div className="lg:col-span-1">
            <StickyContainer 
              topOffset={80} 
              className="w-full" 
              stickyClass="w-full"
              smoothTransition={true}
            >
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 ease-in-out hover:shadow-xl">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-l from-primary-50/40 via-primary-50/20 to-white dark:from-primary-900/20 dark:via-primary-900/10 dark:to-gray-800">
                  <h3 className="text-md font-semibold text-gray-900 dark:text-white flex items-center">
                    <FiSettings className="ml-2 text-primary-600 dark:text-primary-400" />
                    أدوات الاختبار
                  </h3>
                </div>
                
                <div className="p-4 space-y-4">
                  {/* أزرار الاختبار */}
                  <div className="space-y-2">
                    <button className="w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-all duration-200 ease-in-out border border-primary-600 hover:border-primary-700 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-2 focus:ring-primary-500/20 shadow-md hover:shadow-lg gap-2">
                      <FiSave className="w-4 h-4" />
                      حفظ الاختبار
                    </button>
                    
                    <button className="w-full bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg transition-all duration-200 ease-in-out border border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 flex items-center justify-center text-sm font-medium focus:outline-none focus:ring-2 focus:ring-gray-500/20 shadow-md hover:shadow-lg gap-2">
                      <FiX className="w-4 h-4" />
                      إلغاء
                    </button>
                  </div>
                  
                  {/* معلومات الاختبار */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                      معلومات الاختبار
                    </h4>
                    <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex justify-between">
                        <span>الحالة:</span>
                        <span className="text-green-600 dark:text-green-400">نشط</span>
                      </div>
                      <div className="flex justify-between">
                        <span>النوع:</span>
                        <span>StickyContainer</span>
                      </div>
                      <div className="flex justify-between">
                        <span>الإصدار:</span>
                        <span>2.0 محسّن</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* ملاحظات */}
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">
                      ملاحظات الاختبار
                    </h4>
                    <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                      <li>• يجب أن يصبح ثابتاً فوراً عند لمس الحافة</li>
                      <li>• حركة سلسة مع تأثيرات بصرية</li>
                      <li>• يعمل مع جميع أحجام الشاشات</li>
                      <li>• متوافق مع الوضع المظلم</li>
                    </ul>
                  </div>
                </div>
              </div>
            </StickyContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StickyContainerTest;
